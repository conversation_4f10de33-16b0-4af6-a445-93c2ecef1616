<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <!-- 一审列表 -->
  <UmvContent>
    <!-- 查询条件 -->
    <UmvQuery
      v-model="searchData"
      :opts="queryOpts"
      label-width="90px"
      @check="handleSearch"
      @reset="handleReset"
      isExpansion
    >
      <template #footerBtn>
        <ThePictureSearchBtn
          ref="PictureSearchBtn"
          :page-num="query.page"
          :page-size="query.pageSize"
          :type="type"
          :reviewNo="reviewNo"
          :submit="PictureSearchSubmit"
        />
      </template>
    </UmvQuery>
    <!-- 表格 -->
    <UmvTable v-loading="loading" :data="tableData" :columns="columns" @refresh="queryPageList">
      <!-- 工具栏 -->
      <template #tools>
        <el-button type="primary" size="small" v-track:click.btn @click="openWarnDialog">
          预警
        </el-button>
      </template>

      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-model:page="query.page"
          v-model:limit="query.pageSize"
          :total="query.total"
          @pagination="queryPageList"
        />
      </template>
    </UmvTable>
    <!-- 审核弹窗 -->
    <auditDlg
      v-if="orderAudit"
      v-model="orderAudit"
      :orderInfo="orderInfo"
      :auditStatus="type"
      :next-step="nextStep"
      :last-step="lastStep"
      :nextBtn="nextBtn"
      :lastBtn="lastBtn"
    />
    <addServiceDlg v-if="handleRule" v-model="handleRule" />
  </UmvContent>
</template>

<script setup lang="tsx">
import { ref, reactive, onMounted, onActivated, nextTick, computed } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import {
  ElMessage,
  ElMessageBox,
  ElButton,
  ElInput,
  ElDatePicker,
  ElImage,
  ElSelect,
  ElOption
} from 'element-plus'

// 组件导入
import UmvContent from '@/components/UmvContent'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import auditDlg from '../components/auditDlg.vue'
import addServiceDlg from '../components/addServiceDlg.vue'
import ThePictureSearchBtn from '@/views/RuleManage/components/ThePictureSearchBtn'

// API 和工具函数
import { getAuditListApi } from '@/api/RuleManage/PictureAudit'
import { useCustomerReviewSearch } from '@/views/RuleManage/common/useCustomerReviewSearch'
import { CustomerSelect } from '@/components/CustomerSelect/index'

// 定义组件名称
defineOptions({
  name: 'FirstAudit'
})

const { t } = useI18n()
const props = withDefaults(defineProps<{ type?: '100' | '200' | '300' | '9999' }>(), {
  type: '100'
})

// 使用客户审核搜索hook
const { customerId, reviewNo, reviewConfiglist, changeCusNo, changeReviewNo } =
  useCustomerReviewSearch()

const loading = ref(false)
const handleRule = ref(false) // 新增审核项弹窗
const orderAudit = ref(false) // 新增审核项弹窗
const edit = ref(false) // 是否为编辑
const tableData = ref<any[]>([]) // 用户列表
const query = reactive({
  total: 0,
  page: 1,
  pageSize: 10
})
const searchData = ref({
  orderNo: '', //调用方ID
  time: [] as string[], // 订单创建时间
  orderUcode: ''
})

// 查询配置
const queryOpts = ref<Record<string, QueryOption>>({
  customerNo: {
    label: '客户',
    defaultVal: '',
    controlRender: () => (
      <CustomerSelect
        v-model={customerId.value}
        clearable
        onChange={changeCusNo}
        filterable
        placeholder="请选择客户"
      />
    )
  },
  reviewNo: {
    label: '图审服务',
    defaultVal: '',
    controlRender: () => (
      <el-select
        disabled={!customerId.value}
        v-model={reviewNo.value}
        onChange={changeReviewNo}
        clearable
        placeholder="请选择图审服务"
        filterable
        style="width: 100%"
      >
        {reviewConfiglist.value.map((item: any) => (
          <el-option key={item.reviewNo} label={item.reviewName} value={item.reviewNo} />
        ))}
      </el-select>
    )
  },
  orderNo: {
    label: '调用方ID',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput
        v-model={form.orderNo}
        clearable
        maxlength="40"
        placeholder="请输入调用方ID"
        style="width: 100%"
      />
    )
  },
  orderUcode: {
    label: 'U码',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput
        v-model={form.orderUcode}
        clearable
        maxlength="40"
        placeholder="请输入U码"
        style="width: 100%"
      />
    )
  },
  time: {
    label: '创建时间',
    defaultVal: [],
    controlRender: (form: any) => (
      <ElDatePicker
        v-model={form.time}
        type="daterange"
        value-format="YYYY-MM-DD"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="width: 100%"
      />
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'index',
    label: '序号',
    width: '60px',
    renderTemplate: (scope) => <span>{scope.$index + 1}</span>
  },
  {
    prop: 'imageThumbUrl',
    label: '审核图片',
    width: '100px',
    renderTemplate: (scope) => (
      <ElImage
        v-if={scope.row.auditImage}
        fit="fill"
        src={scope.row.auditImage}
        onClick={() => openAuditDialog(scope.row)}
      />
    )
  },
  { prop: 'orderUcode', label: 'U码（别名)', minWidth: '150px' },
  {
    prop: 'reviewName',
    label: '图审服务',
    minWidth: '120px',
    renderTemplate: (scope) => (
      <span
        style={{
          color: scope.row.overTime || scope.row.orderExpedited > 0 ? '#f67777' : 'inherit'
        }}
      >
        {scope.row.reviewName}
      </span>
    )
  },
  { prop: 'customerName', label: '客户', minWidth: '150px' },
  { prop: 'orderNo', label: '调用方ID', minWidth: '150px' },
  { prop: 'createTime', label: '创建时间', minWidth: '140px' },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: '100px',
    renderTemplate: (scope) => (
      <ElButton text type="primary" v-track:click_btn onClick={() => openAuditDialog(scope.row)}>
        审核
      </ElButton>
    )
  }
])

// 获取信息列表
const queryPageList = async (load: boolean = true) => {
  if (loading.value) return // 防止多次发起请求（例如先切换页码，进行条件查询会触发两次请求）
  try {
    loading.value = load
    if (isPictureSearch.value) {
      await PictureSearch()
    } else {
      const data = {
        reviewFlow: Number(props.type),
        customerNo: customerId.value, //客户
        reviewNo: reviewNo.value, //服务编号
        orderNo: searchData.value.orderNo, //调用方ID
        startTime: searchData.value?.time?.[0] ?? '', //创建时间-开始
        endTime: searchData.value?.time?.[1] ?? '', //创建时间-开始
        orderUcode: searchData.value.orderUcode,
        pageNum: query.page,
        pageSize: query.pageSize
      }
      const resData = await getAuditListApi(data)
      tableData.value = resData?.list || []
      query.total = resData?.total || 0
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}
// 关闭弹窗
const closeDialog = () => {
  console.log('')
  orderAudit.value = false
  handleRule.value = false
  edit.value = false
}

// 查询
const handleSearch = () => {
  // isSearch.value = !!searchForm.itemId
  isPictureSearch.value = false
  query.page = 1
  query.total = 0
  queryPageList()
}

// 重置
const handleReset = () => {
  customerId.value = ''
  reviewNo.value = ''
  searchData.value.orderNo = ''
  searchData.value.time = []
  searchData.value.orderUcode = ''
  isPictureSearch.value = false
  query.page = 1
  query.total = 0
  queryPageList()
}

// 搜图功能
const PictureSearchBtn = ref()
const isPictureSearch = ref(false)
async function PictureSearchSubmit() {
  isPictureSearch.value = true
  await PictureSearch()
}
async function PictureSearch() {
  const data = await PictureSearchBtn.value.getList()
  tableData.value = data?.list || []
  query.total = data?.total || 0
}

onMounted(() => {
  handleReset()
})

onActivated(() => {
  queryPageList()
})
//图审弹框上下步操作

const nextBtn = ref(false)
async function nextStep() {
  //若没有下一条，则直接关闭弹框
  if (!nextBtn.value) {
    orderAudit.value = false
    queryPageList()
    nextTick(() => {
      ElMessage.warning('已经是最后一条数据')
    })
    return
  }

  //用来跳转的定位
  let index = tableData.value.findIndex((item) => item.orderId == curOrderId.value)
  //若已经是当前页最后一个了，则下一页
  if (query.pageSize < index + 2) {
    query.page = query.page + 1
    index = 0
  }
  await queryPageList(false)
  //获取更新数据后当前弹框id的位置
  const index2 = tableData.value.findIndex((item) => item.orderId == curOrderId.value)
  const length = tableData.value.length
  //如果能找到，则直接跳到此位置的下一条，否则直接定位到原始位置即可
  if (index2 > -1) {
    index = index2 + 1
  }
  openAuditDialog(tableData.value?.[index] || tableData.value[length - 1])
}
const lastBtn = ref(false)
async function lastStep() {
  //若没有上一条，则直接关闭弹框
  if (!lastBtn.value) {
    queryPageList()
    nextTick(() => {
      ElMessage.warning('已经是最上一条数据')
    })
    return
  }
  //用来跳转的定位
  let index = tableData.value.findIndex((item) => item.orderId == curOrderId.value)
  console.log('lastStep', index)
  //若已经是当前页最前一个了，则上一页
  if (index == 0) {
    query.page = query.page - 1
    index = query.pageSize - 1
  }
  await queryPageList(false)
  //获取更新数据后当前弹框id的位置
  const index2 = tableData.value.findIndex((item) => item.orderId == curOrderId.value)
  //如果能找到，则直接跳到此位置的上一条
  if (index2 > -1) {
    index = index2 - 1
  }
  openAuditDialog(tableData.value?.[index] || tableData.value[0])
}
// 打开弹窗
const orderInfo = ref<any>({}) // 订单信息
const curOrderId = computed(() => orderInfo.value?.orderId || '')

function openAuditDialog(orderInfoDate: any) {
  //若没有参数了，则关闭弹框
  if (!orderInfoDate) {
    orderAudit.value = false
    nextTick(() => {
      ElMessage.warning('最后一条数据已审核完')
    })
    return
  }
  //若下一条是不同服务也进行终止，当orderAudit.value才是连续操作，才需要进行判断
  if (
    orderAudit.value &&
    orderInfo.value?.reviewNo &&
    orderInfo.value?.reviewNo != orderInfoDate?.reviewNo
  ) {
    ElMessageBox.alert(`已是《${orderInfo.value.reviewName}》服务最后一条数据`, '', {
      confirmButtonText: '确认'
    })
    orderAudit.value = false
    return
  }
  const index = tableData.value.findIndex((item) => item.orderId == orderInfoDate.orderId)
  //length要大于index，否则则弹框内没有下一条
  nextBtn.value = !(
    tableData.value.length - 1 <= index && query.page >= Math.ceil(query.total / query.pageSize)
  )
  lastBtn.value = index != 0 || query.page != 1
  orderAudit.value = true
  orderInfo.value = orderInfoDate
}
const openWarnDialog = () => {
  handleRule.value = true
}
</script>

<style scoped lang="less"></style>
