<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <!-- 项目列表 -->
  <UmvContent>
    <!-- 查询条件 -->
    <UmvQuery
      v-model="searchData"
      :opts="queryOpts"
      label-width="90px"
      @check="handleSearch"
      @reset="handleReset"
    />

    <!-- 表格 -->
    <UmvTable v-loading="loading" :data="tableData" :columns="columns" @refresh="queryPageList">
      <!-- 工具栏 -->
      <template #tools>
        <el-button type="primary" size="small" v-track:click.btn @click="openDialog('add')">
          {{ t('common.add') }}
        </el-button>
      </template>

      <!-- 分页 -->
      <template #pagination>
        <Pagination
          :current-page="query.page"
          :page-size="query.pageSize"
          :total="query.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </template>
    </UmvTable>

    <!-- 新增、编辑项目弹窗 -->
    <auditServiceDlg
      v-if="handleRule"
      :handleRule="handleRule"
      :edit="edit"
      :ruleInfo="ruleInfo"
      @closeDialog="closeDialog"
    />
    <reviewNoteDlg
      v-if="reviewRule"
      :reviewRule="reviewRule"
      :ruleInfo="ruleInfo"
      @closeDialog="closeDialog"
    />
  </UmvContent>
</template>

<script setup lang="tsx">
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from '@/hooks/web/useI18n'
import { ElButton, ElSwitch, ElSelect, ElOption } from 'element-plus'

// 组件导入
import UmvContent from '@/components/UmvContent'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import auditServiceDlg from './components/auditServiceDlg.vue'
import reviewNoteDlg from './components/reviewNoteDlg.vue'

// API 和工具函数
import { statusChangeApi, getPageList } from '@/api/RuleManage'
import useMessage from '@/utils/useMessage'
import { useCustomerReviewSearch } from '@/views/RuleManage/common/useCustomerReviewSearch'
import { CustomerSelect } from '@/components/CustomerSelect/index'

// 定义组件名称
defineOptions({
  name: 'AuditService'
})

const { t } = useI18n()
const message = useMessage()
const router = useRouter()

const loading = ref(false)
const handleRule = ref(false) // 新增审核项弹窗
const reviewRule = ref(false) // 审核内容弹窗
const edit = ref(false) // 是否为编辑
const query = ref({
  total: 0,
  page: 1,
  pageSize: 10
})
const searchData = ref({
  reviewNo: '', // 服务编号
  customerNo: '' //客户
})
const ruleInfo = ref<any>({}) // 规则信息

// 使用客户审核搜索hook
const { customerId, reviewNo, reviewConfiglist, changeCusNo, changeReviewNo } =
  useCustomerReviewSearch()

// 查询配置
const queryOpts = ref<Record<string, QueryOption>>({
  customerNo: {
    label: '客户',
    defaultVal: '',
    controlRender: () => (
      <CustomerSelect
        v-model={customerId.value}
        clearable
        onChange={changeCusNo}
        filterable
        placeholder="请选择客户"
      />
    )
  },
  reviewNo: {
    label: '图审服务',
    defaultVal: '',
    controlRender: () => (
      <el-select
        disabled={!customerId.value}
        v-model={reviewNo.value}
        onChange={changeReviewNo}
        clearable
        placeholder="请选择图审服务"
        filterable
        style="width: 100%"
      >
        {reviewConfiglist.value.map((item: any) => (
          <el-option key={item.reviewNo} label={item.reviewName} value={item.reviewNo} />
        ))}
      </el-select>
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'index',
    label: '序号',
    width: '60px',
    renderTemplate: (scope) => (
      <span>{scope.$index + 1 + (query.value.page - 1) * query.value.pageSize}</span>
    )
  },
  { prop: 'reviewName', label: '图审服务', minWidth: '150px' },
  { prop: 'customerName', label: '客户', minWidth: '150px' },
  { prop: 'reviewNo', label: '服务编号', minWidth: '150px' },
  { prop: 'orgName', label: '机构', minWidth: '120px' },
  {
    prop: 'chargeFlag',
    label: '是否收费',
    minWidth: '100px',
    renderTemplate: (scope) => <span>{scope.row.chargeFlag ? '是' : '否'}</span>
  },
  {
    prop: 'reviewStatus',
    label: '状态',
    fixed: 'right',
    minWidth: '90px',
    renderTemplate: (scope) => (
      <ElSwitch
        active-value={0}
        inactive-value={1}
        active-text={t('common.open')}
        inactive-text={t('common.close')}
        inline-prompt
        v-model={scope.row.reviewStatus}
        onClick={() => statusChange(scope.row)}
      />
    )
  },
  { prop: 'createTime', label: '创建时间', minWidth: '120px' },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: '180px',
    renderTemplate: (scope) => (
      <div>
        <ElButton
          type="primary"
          link
          v-track:click_btn
          onClick={() => openDialog('edit', scope.row)}
        >
          {t('common.edit')}
        </ElButton>
        <ElButton
          type="primary"
          link
          v-track:click_btn
          onClick={() => openDialog('set', scope.row)}
        >
          配置
        </ElButton>
        <ElButton
          type="primary"
          link
          v-track:click_btn
          onClick={() => openDialog('note', scope.row)}
        >
          审核要求
        </ElButton>
      </div>
    )
  }
])

// 状态修改
const statusChange = async (obj: any) => {
  const data = {
    reviewConfigId: obj.reviewConfigId,
    reviewStatus: obj.reviewStatus
  }
  loading.value = true
  try {
    const res = await statusChangeApi(data)
    if (res?.code == 200 || res?.code == 0) {
      message.success('操作成功')
      queryPageList()
    }
  } catch (error) {
    obj.reviewStatus = obj.reviewStatus == 0 ? 1 : 0
  } finally {
    loading.value = false
  }
}

// 获取信息列表
const tableData = ref<any[]>([])
const queryPageList = async () => {
  if (loading.value) {
    return
  }
  const data = {
    customerNo: customerId.value,
    reviewNo: reviewNo.value,
    pageNum: query.value.page,
    pageSize: query.value.pageSize
  }
  loading.value = true
  try {
    const resData = await getPageList(data)
    tableData.value = resData?.list || []
    query.value.total = resData?.total || 0
  } finally {
    loading.value = false
  }
}

onActivated(() => {
  queryPageList()
})

onMounted(() => {
  queryPageList()
})

// 打开项目弹窗
const openDialog = (type: string, obj?: any) => {
  console.log(obj, type)
  switch (type) {
    // 新增
    case 'add':
      handleRule.value = true
      edit.value = false
      break
    // 编辑
    case 'edit':
      edit.value = true
      ruleInfo.value = obj
      handleRule.value = true
      break
    // 审核要求
    case 'note':
      reviewRule.value = true
      ruleInfo.value = obj
      break
    // 配置
    case 'set':
      router.push({
        path: '/RuleManage/RuleManage-RuleConfigList',
        query: {
          id: obj.reviewNo,
          configid: obj.reviewConfigId
        }
      })
      break
  }
}
// 关闭添加服务弹窗
const closeDialog = (result?: any) => {
  edit.value = false
  handleRule.value = false
  reviewRule.value = false
  if (result) {
    queryPageList()
  }
}

// 搜索
const handleSearch = () => {
  queryPageList()
}
// 重置
const handleReset = () => {
  customerId.value = ''
  reviewNo.value = ''
  query.value.page = 1
  query.value.total = 0
  queryPageList()
}

// 改变页数
const handlePageChange = (val: number) => {
  query.value.page = val
  queryPageList()
}
// 改变条数
const handleSizeChange = (val: number) => {
  query.value.page = 1
  query.value.pageSize = val
  queryPageList()
}
</script>
