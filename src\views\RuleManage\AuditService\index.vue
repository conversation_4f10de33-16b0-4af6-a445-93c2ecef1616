<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <!-- 项目列表 -->
  <div class="project-list-wrap">
    <!-- 查询 -->
    <el-card class="box-card">
      <el-form class="demo-ruleForm" label-width="80px" label-position="right">
        <el-row :gutter="20">
          <customerReviewSearch
            v-model:customerId="searchData.customerNo"
            v-model:reviewNo="searchData.reviewNo"
          />
          <el-col :span="6">
            <el-button type="primary" v-track:click.btn @click="handleSearch">{{
              t('common.query')
            }}</el-button>
            <el-button type="warning" @click="handleReset">{{ t('common.reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 列表 -->
      <el-button class="margin-b-10" type="primary" v-track:click.btn @click="openDialog('add')">{{
        t('common.add')
      }}</el-button>
      <el-table
        v-loading="loading"
        :data="tableData"
        :header-cell-style="{ background: '#F7F7F9', color: '#606266' }"
        style="margin-top: 10px; width: 100%; border: 1px solid #f1f1f1"
      >
        <el-table-column label="序号" width="60px">
          <template #default="scope">
            <!-- {{ scope.$index + 1 }} -->
            {{ scope.$index + 1 + (query.page - 1) * query.pageSize }}
          </template>
        </el-table-column>
        <el-table-column prop="reviewName" label="图审服务" min-width="150" />
        <!-- <el-table-column prop="itemName" label="项目" min-width="150" /> -->
        <el-table-column prop="customerName" label="客户" min-width="150" />
        <el-table-column prop="reviewNo" label="服务编号" min-width="150" />
        <el-table-column prop="orgName" label="机构" min-width="120" />
        <el-table-column prop="chargeFlag" label="是否收费" min-width="100">
          <template #default="scope">
            {{ scope.row.chargeFlag ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="reviewStatus" fixed="right" label="状态" min-width="90">
          <template #default="scope">
            <el-switch
              class="switch"
              :active-value="0"
              :inactive-value="1"
              :active-text="t('common.open')"
              :inactive-text="t('common.close')"
              v-model="scope.row.reviewStatus"
              active-color="#1890FF"
              inactive-color="rgba(0, 0, 0, 0.25)"
              @click="statusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="120" />
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button
              type="primary"
              link
              v-track:click.btn
              @click="openDialog('edit', scope.row)"
              >{{ t('common.edit') }}</el-button
            >
            <el-button type="primary" link v-track:click.btn @click="openDialog('set', scope.row)"
              >配置</el-button
            >
            <el-button type="primary" link v-track:click.btn @click="openDialog('note', scope.row)"
              >审核要求</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <div style="margin-top: 20px; text-align: right">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="query.page"
          :page-sizes="[10, 30, 50]"
          :page-size="query.pageSize"
          :total="query.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
    <!-- 新增、编辑项目弹窗 -->
    <auditServiceDlg
      v-if="handleRule"
      :handleRule="handleRule"
      :edit="edit"
      :ruleInfo="ruleInfo"
      @closeDialog="closeDialog"
    />
    <reviewNoteDlg
      v-if="reviewRule"
      :reviewRule="reviewRule"
      :ruleInfo="ruleInfo"
      @closeDialog="closeDialog"
    />
  </div>
</template>

<script setup name="AuditService" lang="ts">
const { t } = useI18n()
import customerReviewSearch from '@/views/RuleManage/components/customerReviewSearch'
import auditServiceDlg from './components/auditServiceDlg.vue'
import reviewNoteDlg from './components/reviewNoteDlg.vue'
import { useRouter } from 'vue-router'
import { statusChangeApi, getPageList } from '@/api/RuleManage'
// import $http from '@/utils/request'
import useMessage from '@/utils/useMessage'
const message = useMessage()
// 获取路由器实例
const router = useRouter()

const loading = ref(false)
const handleRule = ref(false) // 新增审核项弹窗
const reviewRule = ref(false) // 审核内容弹窗
const deleteRule = ref(false) // 删除弹窗
const edit = ref(false) // 是否为编辑
const query = ref({
  total: 0,
  page: 1,
  pageSize: 10
})
const searchData = ref({
  reviewNo: '', // 服务编号
  // itemNo: '', // 项目
  reviewName: '', // 图审服务
  customerNo: '' //客户
})
let ruleInfo = reactive({}) // 规则信息

// 状态修改
const statusChange = async (obj) => {
  const data = {
    reviewConfigId: obj.reviewConfigId,
    reviewStatus: obj.reviewStatus
  }
  loading.value = true
  try {
    const res = await statusChangeApi(data)
    if (res?.code == 200 || res?.code == 0) {
      message.success('操作成功')
      queryPageList()
    }
  } catch (error) {
    obj.reviewStatus = obj.reviewStatus == 0 ? 1 : 0
  } finally {
    loading.value = false
  }
}

// 获取信息列表
const tableData = ref<any[]>([])
const queryPageList = async () => {
  if (loading.value) {
    return
  }
  const data = {
    // itemNo: searchData.itemNo,
    customerNo: searchData.value.customerNo,
    reviewName: searchData.value.reviewName,
    reviewNo: searchData.value.reviewNo,
    pageNum: query.value.page,
    pageSize: query.value.pageSize
  }
  loading.value = true
  try {
    const resData = await getPageList(data)
    tableData.value = resData?.list || []
    query.value.total = resData?.total || 0
  } finally {
    loading.value = false
  }
}

onActivated(() => {
  queryPageList()
})

onMounted(() => {
  queryPageList()
})

const userStatus = (value) => {
  return value === '0' ? '正常' : '停用'
}
// 打开项目弹窗
const openDialog = (type, obj?) => {
  console.log(obj, type)
  switch (type) {
    // 新增
    case 'add':
      handleRule.value = true
      edit.value = false
      break
    // 编辑
    case 'edit':
      edit.value = true
      ruleInfo = obj
      handleRule.value = true
      break
    // 审核要求
    case 'note':
      reviewRule.value = true
      ruleInfo = obj
      break
    // 配置
    case 'set':
      router.push({
        path: '/RuleManage/RuleManage-RuleConfigList',
        query: {
          id: obj.reviewNo,
          configid: obj.reviewConfigId
        }
      })
      break
  }
}
// 关闭添加服务弹窗
const closeDialog = (result) => {
  edit.value = false
  handleRule.value = false
  deleteRule.value = false
  reviewRule.value = false
  if (result) {
    queryPageList()
  }
}

// 关闭删除服务弹窗
const handleClose = (done) => {
  deleteRule.value = false
}

// 搜索
const handleSearch = () => {
  queryPageList()
}
// 重置
const handleReset = () => {
  searchData.value.reviewName = ''
  searchData.value.customerNo = ''
  searchData.value.reviewNo = ''

  query.value.page = 1
  query.value.total = 0
  queryPageList()
}

// 改变页数
const handlePageChange = (val) => {
  query.value.page = val
  queryPageList()
}
// 改变条数
const handleSizeChange = (val) => {
  query.value.page = 1
  query.value.pageSize = val
  queryPageList()
}
</script>

<style scoped lang="less">
.project-list-wrap {
  .el-select,
  .el-cascader {
    width: 100%;
  }
  .el-date-editor {
    /* min-width: 240px; */
    width: 100%;
  }

  .searchForm {
    width: 100%;
  }

  /* switch按钮样式 */
  :deep(.switch .el-switch__label) {
    position: absolute;
    display: none;
    color: #fff !important;
  }

  /*打开时文字位置设置*/
  :deep(.switch .el-switch__label--right) {
    z-index: 1;
  }

  /* 调整打开时文字的显示位子 */
  :deep(.switch .el-switch__label--right span) {
    margin-left: 9px;
  }

  /*关闭时文字位置设置*/
  :deep(.switch .el-switch__label--left) {
    z-index: 1;
  }

  /* 调整关闭时文字的显示位子 */
  :deep(.switch .el-switch__label--left span) {
    margin-left: 22px;
  }

  /*显示文字*/
  :deep(.switch .el-switch__label.is-active) {
    display: block;
  }

  /* 调整按钮的宽度 */
  :deep(.switch.el-switch .el-switch__core),
  :deep(.el-switch .el-switch__label) {
    width: 60px !important;
    margin: 0;
  }
}
</style>
