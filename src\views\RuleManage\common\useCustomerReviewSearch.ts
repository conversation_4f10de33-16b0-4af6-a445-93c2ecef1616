import { ref, nextTick } from 'vue'
import { getCustomerList, getPageList } from '@/api/RuleManage'

// 默认值存储
const customerIdDefault = ref('')
const reviewNoDefault = ref('')
const reviewConfiglistDefault = ref<any[]>([])
const allReviewConfiglistDefault = ref<any[]>([])

// 客户列表缓存
const customerList = ref([])
let loading = false
let allReviewLoading = false

// 更新客户列表
async function updateCustomerList() {
  if (!loading) {
    loading = true
    const resData = await getCustomerList({ type: 2 })
    customerList.value = resData || []
    // 2秒内只更新一次
    setTimeout(() => {
      loading = false
    }, 2000)
  }
}

// 获取所有图审服务列表（不受客户约束）
async function updateAllReviewList() {
  if (!allReviewLoading) {
    allReviewLoading = true
    const data = {
      customerNo: '',
      reviewName: '',
      reviewNo: '',
      pageNum: '1',
      pageSize: '999999'
    }
    const resData = await getPageList(data)
    allReviewConfiglistDefault.value = resData?.list || []
    // 2秒内只更新一次
    setTimeout(() => {
      allReviewLoading = false
    }, 2000)
  }
}

// 更新默认值
type DefaultType = {
  customerId: string
  reviewNo: string
  reviewConfiglist: any[]
  allReviewConfiglist?: any[]
}

function updateDefault(defaultObj: DefaultType) {
  customerIdDefault.value = defaultObj.customerId
  reviewNoDefault.value = defaultObj.reviewNo
  reviewConfiglistDefault.value = defaultObj.reviewConfiglist
  if (defaultObj.allReviewConfiglist) {
    allReviewConfiglistDefault.value = defaultObj.allReviewConfiglist
  }
}

// 主要的hook函数
export function useCustomerReviewSearch() {
  const customerId = ref(customerIdDefault.value)
  const reviewNo = ref(reviewNoDefault.value)
  const reviewConfiglist = ref(reviewConfiglistDefault.value)
  const allReviewConfiglist = ref(allReviewConfiglistDefault.value)

  // 客户变更处理
  const changeCusNo = async (value: string | undefined) => {
    if (value === undefined) {
      value = ''
      customerId.value = ''
    }

    await nextTick(() => {
      if (value) {
        // 选择了客户，加载该客户的图审服务
        updateDefault({
          customerId: value,
          reviewNo: reviewNo.value,
          reviewConfiglist: []
        })
        queryImgServiceList()
      } else {
        // 没有选择客户，使用所有图审服务列表
        reviewConfiglist.value = allReviewConfiglist.value
        updateDefault({
          customerId: '',
          reviewNo: reviewNo.value,
          reviewConfiglist: allReviewConfiglist.value
        })
      }
    })
  }

  // 获取图审服务列表（根据客户）
  const queryImgServiceList = async () => {
    const data = {
      customerNo: customerId.value,
      reviewName: '',
      reviewNo: '',
      pageNum: '1',
      pageSize: '999999'
    }
    const resData = await getPageList(data)
    reviewConfiglist.value = resData?.list || []
    updateDefault({
      customerId: customerId.value,
      reviewNo: reviewNo.value,
      reviewConfiglist: reviewConfiglist.value
    })
  }

  // 图审服务变更处理
  const changeReviewNo = (value: string | undefined) => {
    if (value === undefined) {
      value = ''
      reviewNo.value = ''
    }
    updateDefault({
      customerId: customerId.value,
      reviewNo: value || '',
      reviewConfiglist: reviewConfiglist.value,
      allReviewConfiglist: allReviewConfiglist.value
    })
  }

  // 初始化数据
  const initData = async () => {
    // 初始化客户列表
    updateCustomerList()
    // 初始化所有图审服务列表
    await updateAllReviewList()
    // 如果没有选择客户，默认显示所有图审服务
    if (!customerId.value) {
      reviewConfiglist.value = allReviewConfiglistDefault.value
      allReviewConfiglist.value = allReviewConfiglistDefault.value
    }
  }

  // 立即初始化
  initData()

  return {
    customerId,
    reviewNo,
    reviewConfiglist,
    allReviewConfiglist,
    customerList,
    changeCusNo,
    changeReviewNo,
    queryImgServiceList,
    initData
  }
}
