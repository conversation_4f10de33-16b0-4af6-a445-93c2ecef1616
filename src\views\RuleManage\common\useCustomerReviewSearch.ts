import { ref, nextTick } from 'vue'
import { getCustomerList, getPageList } from '@/api/RuleManage'

// 默认值存储
const customerIdDefault = ref('')
const reviewNoDefault = ref('')
const reviewConfiglistDefault = ref<any[]>([])

// 客户列表缓存
const customerList = ref([])
let loading = false

// 更新客户列表
async function updateCustomerList() {
  if (!loading) {
    loading = true
    const resData = await getCustomerList({ type: 2 })
    customerList.value = resData || []
    // 2秒内只更新一次
    setTimeout(() => {
      loading = false
    }, 2000)
  }
}

// 更新默认值
type DefaultType = {
  customerId: string
  reviewNo: string
  reviewConfiglist: any[]
}

function updateDefault(defaultObj: DefaultType) {
  customerIdDefault.value = defaultObj.customerId
  reviewNoDefault.value = defaultObj.reviewNo
  reviewConfiglistDefault.value = defaultObj.reviewConfiglist
}

// 主要的hook函数
export function useCustomerReviewSearch() {
  const customerId = ref(customerIdDefault.value)
  const reviewNo = ref(reviewNoDefault.value)
  const reviewConfiglist = ref(reviewConfiglistDefault.value)

  // 客户变更处理
  const changeCusNo = async (value: string | undefined) => {
    if (value === undefined) {
      value = ''
      customerId.value = ''
    }
    reviewNo.value = ''

    await nextTick(() => {
      if (value) {
        updateDefault({
          customerId: value,
          reviewNo: '',
          reviewConfiglist: []
        })
        queryImgServiceList()
      } else {
        reviewConfiglist.value = []
      }
    })
  }

  // 获取图审服务列表
  const queryImgServiceList = async () => {
    const data = {
      customerNo: customerId.value,
      reviewName: '',
      reviewNo: '',
      pageNum: '1',
      pageSize: '999999'
    }
    const resData = await getPageList(data)
    reviewConfiglist.value = resData?.list || []
    updateDefault({
      customerId: customerId.value,
      reviewNo: '',
      reviewConfiglist: reviewConfiglist.value
    })
  }

  // 图审服务变更处理
  const changeReviewNo = (value: string | undefined) => {
    if (value === undefined) {
      value = ''
      reviewNo.value = ''
    }
    if (value) {
      updateDefault({
        customerId: customerId.value,
        reviewNo: value,
        reviewConfiglist: reviewConfiglist.value
      })
    }
  }

  // 初始化客户列表
  updateCustomerList()

  return {
    customerId,
    reviewNo,
    reviewConfiglist,
    customerList,
    changeCusNo,
    changeReviewNo,
    queryImgServiceList
  }
}
